const API_URL = import.meta.env.VITE_REACT_APP_API_URL || 'https://laravel-api.fly.dev/api';

// Get authentication headers
function getAuthHeaders() {
  const token = localStorage.getItem('access_token');
  return {
    'Content-Type': 'application/json',
    'Authorization': token ? `Bearer ${token}` : ''
  };
}

// Helper function to map status codes to labels according to API documentation
function getStatusLabel(status) {
  const statusMap = {
    'en_attente': 'En attente',
    'confirmee': 'Confirmée',
    'en_preparation': 'En préparation',
    'expediee': 'Expédiée',
    'livree': 'Livrée',
    'annulee': 'Ann<PERSON>ée',
    'remboursee': 'Remboursée',
    'retournee': 'Retournée'
  };

  return statusMap[status] || status || 'En attente';
}

// Orders (Commandes)
export async function fetchOrders(params = {}) {
  try {
    const queryParams = new URLSearchParams();

    // Map parameters according to API documentation
    if (params.user_id) queryParams.append('user_id', params.user_id);
    if (params.date_debut) queryParams.append('date_from', params.date_debut);
    if (params.date_fin) queryParams.append('date_to', params.date_fin);
    if (params.page) queryParams.append('page', params.page);
    if (params.per_page) queryParams.append('per_page', params.per_page);
    if (params.status) queryParams.append('status', params.status);
    if (params.payment_status) queryParams.append('payment_status', params.payment_status);
    if (params.sort_by) queryParams.append('sort_by', params.sort_by);
    if (params.sort_direction) queryParams.append('sort_direction', params.sort_direction);

    // Add eager loading for related data
    queryParams.append('with', 'user,produits,paiement,client');

    const url = `${API_URL}/commandes${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
    console.log('Fetching orders from:', url);

    const res = await fetch(url, {
      method: 'GET',
      headers: getAuthHeaders()
    });

    if (!res.ok) {
      const errorText = await res.text();
      console.error('Failed to fetch orders:', {
        status: res.status,
        statusText: res.statusText,
        error: errorText
      });
      throw new Error(`Failed to fetch orders. Status: ${res.status} - ${errorText}`);
    }

    const response = await res.json();
    console.log('API returned response:', response);

    // Check if the response has the expected structure
    if (!response) {
      throw new Error('API returned empty response.');
    }

    // Handle different response structures
    if (response.status === 'success' && response.data) {
      // Standard API response format
      if (response.data.data && Array.isArray(response.data.data)) {
        // Paginated response
        console.log('Found paginated data structure with', response.data.data.length, 'orders');
        const convertedOrders = response.data.data
          .map(convertOrderFromApi)
          .filter(order => order !== null); // Filter out null orders

        return {
          data: convertedOrders,
          current_page: response.data.current_page,
          last_page: response.data.last_page,
          per_page: response.data.per_page,
          total: response.data.total,
          from: response.data.from,
          to: response.data.to
        };
      } else if (Array.isArray(response.data)) {
        // Direct array response
        console.log('Found direct data array with', response.data.length, 'orders');
        return response.data
          .map(convertOrderFromApi)
          .filter(order => order !== null); // Filter out null orders
      } else {
        // Single order
        console.log('Found single order');
        const convertedOrder = convertOrderFromApi(response.data);
        return convertedOrder ? [convertedOrder] : [];
      }
    } else if (Array.isArray(response)) {
      // Direct array response (no wrapper)
      console.log('Found direct array response with', response.length, 'orders');
      return response.map(convertOrderFromApi);
    } else if (response.data) {
      // Fallback: try to extract data
      const ordersData = Array.isArray(response.data) ? response.data : [response.data];
      return ordersData.map(convertOrderFromApi);
    } else {
      // Empty or unexpected response
      console.log('No orders found or unexpected response format');
      return [];
    }
  } catch (error) {
    console.error('Error fetching orders:', error);
    throw error; // Re-throw the error to be handled by the caller
  }
}

export async function fetchOrderById(id) {
  try {
    const res = await fetch(`${API_URL}/commandes/${id}?with=user,produits,paiement,client`, {
      method: 'GET',
      headers: getAuthHeaders()
    });
    if (!res.ok) {
      const errorText = await res.text();
      console.error(`Failed to fetch order ${id}:`, {
        status: res.status,
        statusText: res.statusText,
        error: errorText
      });
      throw new Error(`Failed to fetch order ${id}. Status: ${res.status} - ${errorText}`);
    }

    const response = await res.json();
    console.log(`API returned order ${id} response:`, response);

    // Check if the response has the expected structure
    if (!response) {
      throw new Error(`API returned unexpected format for order ${id}.`);
    }

    // Handle different response formats
    let orderData = null;

    if (response.data) {
      // The order is in response.data
      orderData = response.data;
    } else if (response.status === 'success' && response.message) {
      // The order might be directly in the response
      orderData = response;
    } else {
      // Assume the response is the order itself
      orderData = response;
    }

    return convertOrderFromApi(orderData);
  } catch (error) {
    console.error(`Error fetching order ${id}:`, error);
    throw error; // Re-throw the error to be handled by the caller
  }
}

// Helper function to convert order from API format to our internal format
function convertOrderFromApi(apiOrder) {
  if (!apiOrder) {
    console.warn('convertOrderFromApi: apiOrder is null or undefined');
    return null;
  }

  // Validate required fields
  if (!apiOrder.id) {
    console.error('convertOrderFromApi: apiOrder.id is missing', apiOrder);
    return null;
  }

  // Additional validation for critical fields
  if (typeof apiOrder.id !== 'number' && typeof apiOrder.id !== 'string') {
    console.error('convertOrderFromApi: apiOrder.id is not a valid type', apiOrder);
    return null;
  }

  console.log('Converting API order:', apiOrder);

  try {
    // Extract customer name from user object first, then fallback to other fields
    const customerName =
      apiOrder.user?.name ||
      apiOrder.nom_client ||
      apiOrder.prenom_client ||
      (apiOrder.email_commande ? apiOrder.email_commande.split('@')[0] : 'Client');

    // Extract shipping address from JSON field or individual fields
    const shippingAddress = apiOrder.shipping_address || {};

    // Safe numeric parsing
    const safeParseFloat = (value, defaultValue = 0) => {
      const parsed = parseFloat(value);
      return isNaN(parsed) ? defaultValue : parsed;
    };

    const safeParseInt = (value, defaultValue = 1) => {
      const parsed = parseInt(value);
      return isNaN(parsed) ? defaultValue : parsed;
    };

    return {
    id: apiOrder.id,
    order_number: apiOrder.numero_commande || `CMD-${String(apiOrder.id).padStart(6, '0')}`,
    customer_id: apiOrder.user_id || apiOrder.client_id,
    customer_name: customerName,
    customer_email: apiOrder.user?.email || apiOrder.email_commande || apiOrder.email || '',
    customer_phone: apiOrder.telephone_commande || apiOrder.telephone || '',

    // Address information
    shipping_name: customerName,
    shipping_address_line1: shippingAddress.street || apiOrder.adresse_commande || apiOrder.adresse || '',
    shipping_city: shippingAddress.city || apiOrder.ville_commande || apiOrder.ville || '',
    shipping_postal_code: shippingAddress.postal_code || apiOrder.code_postal_commande || apiOrder.code_postal || '',
    shipping_country: shippingAddress.country || 'Tunisie',

    // Order totals
    subtotal: parseFloat(apiOrder.subtotal || apiOrder.total_commande || apiOrder.total || 0),
    total: parseFloat(apiOrder.total_commande || apiOrder.total || 0),
    discount: parseFloat(apiOrder.discount_amount || apiOrder.remise_commande || apiOrder.remise || 0),
    shipping_cost: parseFloat(apiOrder.shipping_cost || 0),
    tax: parseFloat(apiOrder.tax_amount || 0),

    // Status information - map according to API documentation
    status: getStatusLabel(apiOrder.status),
    payment_status: apiOrder.payment_status || 'pending',
    payment_method: apiOrder.methode_paiement || 'Carte bancaire',

    // Dates
    created_at: apiOrder.created_at,
    updated_at: apiOrder.updated_at,

    // Items - handle products according to API documentation
    items:
      (apiOrder.produits || []).length > 0
        ? (apiOrder.produits || []).map((product) => ({
            id: product.pivot?.produit_id || product.id,
            product_name: product.nom_produit || product.nom || 'Produit',
            description: product.description || '',
            sku: product.sku || (product.pivot?.produit_id || product.id).toString(),
            unit_price: parseFloat(product.pivot?.prix_unitaire || product.prix || 0),
            quantity: parseInt(product.pivot?.quantite || 1),
            total_price: parseFloat(product.pivot?.total_ligne || (product.pivot?.prix_unitaire || product.prix || 0) * parseInt(product.pivot?.quantite || 1))
          }))
        : [
            {
              id: 1,
              product_name: 'Commande #' + apiOrder.id,
              description: 'Détails non disponibles',
              sku: 'N/A',
              unit_price: parseFloat(apiOrder.total_commande || apiOrder.total || 0),
              quantity: 1,
              total_price: parseFloat(apiOrder.total_commande || apiOrder.total || 0)
            }
          ],

    // Payment information
    payment_info: apiOrder.paiement ? {
      id: apiOrder.paiement.id,
      amount: parseFloat(apiOrder.paiement.montant || 0),
      method: apiOrder.paiement.methode_paiement || apiOrder.methode_paiement,
      status: apiOrder.paiement.status,
      transaction_id: apiOrder.paiement.transaction_id,
      processed_at: apiOrder.paiement.processed_at
    } : null,

    // Additional dates from API
    date_commande: apiOrder.date_commande,
    confirmed_at: apiOrder.confirmed_at,
    preparation_started_at: apiOrder.preparation_started_at,
    shipped_at: apiOrder.shipped_at,
    delivered_at: apiOrder.delivered_at,
    cancelled_at: apiOrder.cancelled_at,
    refunded_at: apiOrder.refunded_at,

    // Notes and promo code
    notes: apiOrder.notes,
    code_promo: apiOrder.code_promo,

    // Original data
    _original: apiOrder
  };
  } catch (error) {
    console.error('Error converting order from API:', error, apiOrder);
    return null;
  }
}

export async function updateOrderStatus(id, status, notes = '', send_notification = true) {
  // Use the specific status update endpoint from API documentation
  const res = await fetch(`${API_URL}/commandes/${id}/status`, {
    method: 'PATCH',
    headers: getAuthHeaders(),
    body: JSON.stringify({
      status,
      notes,
      send_notification
    })
  });

  if (!res.ok) {
    const errorText = await res.text();
    console.error('Failed to update order status:', {
      status: res.status,
      statusText: res.statusText,
      error: errorText
    });
    throw new Error(`Erreur lors de la mise à jour du statut de la commande: ${res.status} - ${errorText}`);
  }

  const response = await res.json();
  return convertOrderFromApi(response.data || response);
}

export async function updateOrder(id, data) {
  const apiData = {
    adresse_commande: data.shipping_address_line1,
    ville_commande: data.shipping_city,
    code_postal_commande: data.shipping_postal_code,
    telephone_commande: data.shipping_phone,
    email_commande: data.shipping_email
  };

  const res = await fetch(`${API_URL}/commandes/${id}`, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(apiData)
  });
  if (!res.ok) throw new Error('Erreur lors de la mise à jour de la commande');

  const responseData = await res.json();
  return convertOrderFromApi(responseData);
}

export async function createOrder(data) {
  // Create order according to API documentation
  const apiData = {
    cart_id: data.cart_id, // Required: shopping cart ID
    shipping_address: {
      street: data.shipping_address_line1,
      city: data.shipping_city,
      postal_code: data.shipping_postal_code,
      country: data.shipping_country || 'Tunisie'
    },
    billing_address: {
      street: data.billing_address_line1 || data.shipping_address_line1,
      city: data.billing_city || data.shipping_city,
      postal_code: data.billing_postal_code || data.shipping_postal_code,
      country: data.billing_country || data.shipping_country || 'Tunisie'
    },
    methode_paiement: data.payment_method || 'stripe',
    notes: data.notes || '',
    code_promo: data.promo_code || '',
    user_id: data.customer_id,
    client_id: data.client_id,
    guest_details: data.guest_details // For guest checkouts
  };

  const res = await fetch(`${API_URL}/commandes`, {
    method: 'POST',
    headers: getAuthHeaders(),
    body: JSON.stringify(apiData)
  });

  if (!res.ok) {
    const errorText = await res.text();
    console.error('Failed to create order:', {
      status: res.status,
      statusText: res.statusText,
      error: errorText
    });
    throw new Error(`Erreur lors de la création de la commande: ${res.status} - ${errorText}`);
  }

  const response = await res.json();
  return convertOrderFromApi(response.data || response);
}

// Process order payment according to API documentation
export async function processOrderPayment(id, paymentDetails) {
  const res = await fetch(`${API_URL}/commandes/${id}/pay`, {
    method: 'POST',
    headers: getAuthHeaders(),
    body: JSON.stringify({
      payment_details: paymentDetails
    })
  });

  if (!res.ok) {
    const errorText = await res.text();
    console.error('Failed to process payment:', {
      status: res.status,
      statusText: res.statusText,
      error: errorText
    });
    throw new Error(`Erreur lors du traitement du paiement: ${res.status} - ${errorText}`);
  }

  const response = await res.json();
  return {
    order: convertOrderFromApi(response.data?.commande || response.commande),
    payment: response.data?.paiement || response.paiement
  };
}

// Cancel order according to API documentation
export async function cancelOrder(id, reason = '') {
  const res = await fetch(`${API_URL}/commandes/${id}/cancel`, {
    method: 'POST',
    headers: getAuthHeaders(),
    body: JSON.stringify({
      reason
    })
  });

  if (!res.ok) {
    const errorText = await res.text();
    console.error('Failed to cancel order:', {
      status: res.status,
      statusText: res.statusText,
      error: errorText
    });
    throw new Error(`Erreur lors de l'annulation de la commande: ${res.status} - ${errorText}`);
  }

  const response = await res.json();
  return convertOrderFromApi(response.data || response);
}

export async function deleteOrder(id) {
  const res = await fetch(`${API_URL}/commandes/${id}`, {
    method: 'DELETE',
    headers: getAuthHeaders()
  });

  if (!res.ok) {
    const errorText = await res.text();
    console.error('Failed to delete order:', {
      status: res.status,
      statusText: res.statusText,
      error: errorText
    });
    throw new Error(`Erreur lors de la suppression de la commande: ${res.status} - ${errorText}`);
  }

  return res.json();
}

// Client Orders
export async function fetchClientOrders(clientId) {
  const res = await fetch(`${API_URL}/clients/${clientId}/commandes`);
  if (!res.ok) throw new Error('Erreur lors du chargement des commandes du client');

  const data = await res.json();
  return Array.isArray(data) ? data.map(convertOrderFromApi) : [];
}

export async function fetchLastClientOrder(clientId) {
  const res = await fetch(`${API_URL}/clients/${clientId}/derniere-commande`);
  if (!res.ok) throw new Error('Erreur lors du chargement de la dernière commande du client');

  const data = await res.json();
  return convertOrderFromApi(data);
}

// Admin Endpoints
export async function fetchLastClientOrderAdmin(clientId) {
  const res = await fetch(`${API_URL}/v1/admin/clients/${clientId}/derniere-commande`);
  if (!res.ok) throw new Error('Erreur lors du chargement de la dernière commande du client (admin)');

  const data = await res.json();
  return convertOrderFromApi(data);
}

// Order statuses according to API documentation
export async function fetchOrderStatuses() {
  // Return statuses according to CommandeStatus enum from API documentation
  console.log('Using order statuses from API documentation');

  return [
    { id: 1, name: 'En attente', value: 'en_attente', color: 'warning' },
    { id: 2, name: 'Confirmée', value: 'confirmee', color: 'info' },
    { id: 3, name: 'En préparation', value: 'en_preparation', color: 'primary' },
    { id: 4, name: 'Expédiée', value: 'expediee', color: 'primary' },
    { id: 5, name: 'Livrée', value: 'livree', color: 'success' },
    { id: 6, name: 'Annulée', value: 'annulee', color: 'danger' },
    { id: 7, name: 'Remboursée', value: 'remboursee', color: 'secondary' },
    { id: 8, name: 'Retournée', value: 'retournee', color: 'warning' }
  ];
}

// Mock order history (not provided by the API)
export async function fetchOrderHistory(orderId) {
  // Return mock history since the API doesn't provide it
  return [
    {
      id: 1,
      order_id: orderId,
      status: 'En attente',
      description: 'Commande créée',
      notes: 'Commande créée par le client',
      created_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString()
    },
    {
      id: 2,
      order_id: orderId,
      status: 'En cours de traitement',
      description: 'Paiement reçu',
      notes: 'Paiement confirmé',
      created_at: new Date(Date.now() - 6 * 24 * 60 * 60 * 1000).toISOString()
    },
    {
      id: 3,
      order_id: orderId,
      status: 'Expédiée',
      description: 'Commande expédiée',
      notes: 'Commande expédiée via Colissimo',
      created_at: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString()
    },
    {
      id: 4,
      order_id: orderId,
      status: 'Livrée',
      description: 'Commande livrée',
      notes: 'Commande livrée au client',
      created_at: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString()
    }
  ];
}
