import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Container, Row, Col, Card, <PERSON>ton, Badge, Alert, Spinner, Table, Tabs, Tab, Form, ListGroup } from 'react-bootstrap';
import {
  FaArrowLeft,
  FaEdit,
  FaFileInvoice,
  FaBoxOpen,
  FaHistory,
  FaCommentAlt,
  FaCheck,
  FaTimes,
  FaShippingFast,
  FaCreditCard,
  FaUser,
  FaMapMarkerAlt,
  FaEnvelope,
  FaPhone
} from 'react-icons/fa';
import { fetchOrderById, updateOrderStatus, fetchOrderStatuses, fetchOrderHistory } from '../../services/orderService';

const OrderDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();

  // State
  const [order, setOrder] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [activeTab, setActiveTab] = useState('details');
  const [orderStatuses, setOrderStatuses] = useState([]);
  const [orderHistory, setOrderHistory] = useState([]);
  const [statusForm, setStatusForm] = useState({
    status: '',
    notes: ''
  });
  const [submitting, setSubmitting] = useState(false);

  // Load order data
  const loadOrder = async () => {
    setLoading(true);
    setError('');
    try {
      const data = await fetchOrderById(id);
      setOrder(data);
      setStatusForm({
        status: data.status,
        notes: ''
      });
    } catch (e) {
      setError(`Erreur lors du chargement de la commande: ${e.message}`);
    }
    setLoading(false);
  };

  // Load order statuses
  const loadOrderStatuses = async () => {
    try {
      const data = await fetchOrderStatuses();
      setOrderStatuses(data);
    } catch (e) {
      console.error('Erreur lors du chargement des statuts de commande:', e);
    }
  };

  // Load order history
  const loadOrderHistory = async () => {
    try {
      const data = await fetchOrderHistory(id);
      setOrderHistory(data);
    } catch (e) {
      console.error("Erreur lors du chargement de l'historique de la commande:", e);
    }
  };

  // Initial data load
  useEffect(() => {
    console.log('OrderDetail component mounted, loading data for order ID:', id);

    // Try to load from API
    loadOrder();
    loadOrderStatuses();
    loadOrderHistory();
  }, [id]);

  // Handle status form changes
  const handleStatusChange = (e) => {
    const { name, value } = e.target;
    setStatusForm({
      ...statusForm,
      [name]: value
    });
  };

  // Update order status
  const handleUpdateStatus = async (e) => {
    e.preventDefault();

    if (!statusForm.status) {
      setError('Veuillez sélectionner un statut');
      return;
    }

    setSubmitting(true);
    setError('');

    try {
      await updateOrderStatus(id, statusForm.status, statusForm.notes);
      setSuccess('Statut de la commande mis à jour avec succès');
      loadOrder();
      loadOrderHistory(); // Reload history after status update
    } catch (e) {
      setError(`Erreur lors de la mise à jour du statut: ${e.message}`);
    }

    setSubmitting(false);
    setTimeout(() => setSuccess(''), 3000);
  };

  // Get status badge variant
  const getStatusVariant = (status) => {
    if (!status) return 'secondary';

    // Direct mapping for common statuses (both French and English)
    const statusColorMap = {
      // French statuses
      'En attente': 'warning',
      'En cours de traitement': 'info',
      Expédiée: 'primary',
      Livrée: 'success',
      Annulée: 'danger',
      Remboursée: 'secondary',
      // English equivalents
      pending: 'warning',
      processing: 'info',
      completed: 'success',
      cancelled: 'danger',
      refunded: 'secondary',
      'on-hold': 'dark',
      shipped: 'primary'
    };

    // Check if we have a direct mapping
    if (statusColorMap[status]) {
      return statusColorMap[status];
    }

    // Check lowercase version
    if (statusColorMap[status.toLowerCase()]) {
      return statusColorMap[status.toLowerCase()];
    }

    // Default fallback
    return 'secondary';
  };

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('fr-FR', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: 'numeric'
    }).format(date);
  };

  // Format price
  const formatPrice = (price) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR'
    }).format(price);
  };

  if (loading) {
    return (
      <Container className="py-4 text-center">
        <Spinner animation="border" variant="primary" />
        <p className="mt-3">Chargement des détails de la commande...</p>
      </Container>
    );
  }

  if (error && !order) {
    return (
      <Container className="py-4">
        <Alert variant="danger">{error}</Alert>
        <Button variant="outline-primary" onClick={() => navigate('/orders')}>
          <FaArrowLeft className="me-2" />
          Retour aux commandes
        </Button>
      </Container>
    );
  }

  return (
    <Container fluid className="py-4">
      {/* Header */}
      <div className="d-flex justify-content-between align-items-center mb-4">
        <div>
          <Button variant="outline-secondary" onClick={() => navigate('/orders')} className="mb-2">
            <FaArrowLeft className="me-2" />
            Retour aux Commandes
          </Button>
          <h2 className="mb-0">Commande #{order?.order_number || order?.id}</h2>
          <p className="text-muted">Passée le {formatDate(order?.created_at)}</p>
        </div>
        <div className="d-flex gap-2">
          <Badge bg={getStatusVariant(order?.status)} className="fs-6 px-3 py-2">
            {order?._original?.status_label || order?.status}
          </Badge>
          <Button variant="outline-primary" size="sm" onClick={() => setActiveTab('status')}>
            <FaEdit className="me-1" /> Modifier le statut
          </Button>
        </div>
      </div>

      {error && <Alert variant="danger">{error}</Alert>}
      {success && <Alert variant="success">{success}</Alert>}

      {/* Tabs */}
      <Card className="shadow-sm mb-4 border-0">
        <Card.Body className="p-0">
          <Tabs activeKey={activeTab} onSelect={setActiveTab} className="mb-0 nav-tabs-custom" fill>
            <Tab
              eventKey="details"
              title={
                <span>
                  <FaFileInvoice className="me-2" />
                  Détails de la commande
                </span>
              }
            />
            <Tab
              eventKey="status"
              title={
                <span>
                  <FaEdit className="me-2" />
                  Modifier le statut
                </span>
              }
            />
            <Tab
              eventKey="history"
              title={
                <span>
                  <FaHistory className="me-2" />
                  Historique
                </span>
              }
            />
          </Tabs>
        </Card.Body>
      </Card>

      {/* Tab Content */}
      {activeTab === 'details' && (
        <>
          <Row>
            <Col md={8}>
              {/* Order Items */}
              <Card className="shadow-sm mb-4 border-0">
                <Card.Header className="bg-white py-3">
                  <h5 className="mb-0">Produits commandés</h5>
                </Card.Header>
                <Card.Body className="p-0">
                  <Table responsive className="mb-0">
                    <thead className="bg-light">
                      <tr>
                        <th>Produit</th>
                        <th>Prix</th>
                        <th>Quantité</th>
                        <th className="text-end">Total</th>
                      </tr>
                    </thead>
                    <tbody>
                      {order?.items?.map((item) => (
                        <tr key={item.id}>
                          <td>
                            <div className="fw-medium">{item.product_name}</div>
                            {item.variant_name && <div className="small text-muted">{item.variant_name}</div>}
                            {item.sku && <div className="small text-muted">SKU: {item.sku}</div>}
                          </td>
                          <td>{formatPrice(item.unit_price)}</td>
                          <td>{item.quantity}</td>
                          <td className="text-end">{formatPrice(item.total_price)}</td>
                        </tr>
                      ))}
                    </tbody>
                    <tfoot className="bg-light">
                      <tr>
                        <td colSpan="3" className="text-end fw-bold">
                          Sous-total:
                        </td>
                        <td className="text-end">{formatPrice(order?.subtotal)}</td>
                      </tr>
                      {order?.discount > 0 && (
                        <tr>
                          <td colSpan="3" className="text-end">
                            Remise:
                          </td>
                          <td className="text-end">-{formatPrice(order?.discount)}</td>
                        </tr>
                      )}
                      <tr>
                        <td colSpan="3" className="text-end">
                          Frais de livraison:
                        </td>
                        <td className="text-end">{formatPrice(order?.shipping_cost)}</td>
                      </tr>
                      <tr>
                        <td colSpan="3" className="text-end">
                          TVA:
                        </td>
                        <td className="text-end">{formatPrice(order?.tax)}</td>
                      </tr>
                      <tr>
                        <td colSpan="3" className="text-end fw-bold fs-5">
                          Total:
                        </td>
                        <td className="text-end fw-bold fs-5">{formatPrice(order?.total)}</td>
                      </tr>
                    </tfoot>
                  </Table>
                </Card.Body>
              </Card>
            </Col>
            <Col md={4}>
              {/* Customer Information */}
              <Card className="shadow-sm mb-4 border-0">
                <Card.Header className="bg-white py-3">
                  <h5 className="mb-0">
                    <FaUser className="me-2" />
                    Client
                  </h5>
                </Card.Header>
                <Card.Body>
                  <p className="fw-medium mb-1">{order?.customer_name}</p>
                  <p className="mb-1">
                    <FaEnvelope className="me-2 text-muted" />
                    {order?.customer_email}
                  </p>
                  {order?.customer_phone && (
                    <p className="mb-0">
                      <FaPhone className="me-2 text-muted" />
                      {order?.customer_phone}
                    </p>
                  )}
                  {order?.customer_id && (
                    <Button variant="outline-primary" size="sm" className="mt-3" onClick={() => navigate(`/clients/${order.customer_id}`)}>
                      Voir le client
                    </Button>
                  )}
                </Card.Body>
              </Card>

              {/* Shipping Information */}
              <Card className="shadow-sm mb-4 border-0">
                <Card.Header className="bg-white py-3">
                  <h5 className="mb-0">
                    <FaMapMarkerAlt className="me-2" />
                    Adresse de livraison
                  </h5>
                </Card.Header>
                <Card.Body>
                  <p className="fw-medium mb-1">{order?.shipping_name}</p>
                  <p className="mb-1">{order?.shipping_address_line1}</p>
                  {order?.shipping_address_line2 && <p className="mb-1">{order?.shipping_address_line2}</p>}
                  <p className="mb-1">
                    {order?.shipping_city}, {order?.shipping_postal_code}
                  </p>
                  <p className="mb-0">{order?.shipping_country}</p>
                </Card.Body>
              </Card>

              {/* Payment Information */}
              <Card className="shadow-sm mb-4 border-0">
                <Card.Header className="bg-white py-3">
                  <h5 className="mb-0">
                    <FaCreditCard className="me-2" />
                    Informations de paiement
                  </h5>
                </Card.Header>
                <Card.Body>
                  <p className="mb-1">
                    <span className="fw-medium">Méthode:</span> {order?.payment_method}
                  </p>
                  <p className="mb-1">
                    <span className="fw-medium">Statut:</span>{' '}
                    <Badge bg={order?.payment_status === 'paid' ? 'success' : 'warning'}>
                      {order?.payment_status === 'paid' ? 'Payé' : 'En attente'}
                    </Badge>
                  </p>
                  {order?.transaction_id && (
                    <p className="mb-0">
                      <span className="fw-medium">ID de transaction:</span> {order?.transaction_id}
                    </p>
                  )}
                </Card.Body>
              </Card>
            </Col>
          </Row>
        </>
      )}

      {activeTab === 'status' && (
        <Card className="shadow-sm border-0">
          <Card.Header className="bg-white py-3">
            <h5 className="mb-0">Modifier le statut de la commande</h5>
          </Card.Header>
          <Card.Body>
            <Form onSubmit={handleUpdateStatus}>
              <Row>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Statut actuel</Form.Label>
                    <div>
                      <Badge bg={getStatusVariant(order?.status)} className="px-3 py-2">
                        {order?._original?.status_label || order?.status}
                      </Badge>
                    </div>
                  </Form.Group>
                </Col>
                <Col md={6}>
                  <Form.Group className="mb-3">
                    <Form.Label>Nouveau statut</Form.Label>
                    <Form.Select name="status" value={statusForm.status} onChange={handleStatusChange} required>
                      <option value="">Sélectionner un statut</option>
                      {orderStatuses.map((status) => (
                        <option key={status.id || status.name} value={status.name}>
                          {status.name}
                        </option>
                      ))}
                    </Form.Select>
                  </Form.Group>
                </Col>
              </Row>
              <Form.Group className="mb-3">
                <Form.Label>Notes (Optionnel)</Form.Label>
                <Form.Control
                  as="textarea"
                  rows={3}
                  name="notes"
                  value={statusForm.notes}
                  onChange={handleStatusChange}
                  placeholder="Ajouter des notes concernant ce changement de statut"
                />
              </Form.Group>
              <div className="d-flex justify-content-end">
                <Button type="submit" variant="primary" disabled={submitting || statusForm.status === order?.status}>
                  {submitting ? (
                    <>
                      <Spinner size="sm" animation="border" className="me-2" />
                      Mise à jour...
                    </>
                  ) : (
                    <>
                      <FaCheck className="me-2" />
                      Mettre à jour le statut
                    </>
                  )}
                </Button>
              </div>
            </Form>
          </Card.Body>
        </Card>
      )}

      {activeTab === 'history' && (
        <Card className="shadow-sm border-0">
          <Card.Header className="bg-white py-3">
            <h5 className="mb-0">Historique de la commande</h5>
          </Card.Header>
          <Card.Body>
            {orderHistory?.length > 0 ? (
              <ListGroup variant="flush">
                {orderHistory.map((event, index) => (
                  <ListGroup.Item key={index} className="px-0">
                    <div className="d-flex justify-content-between">
                      <div>
                        <p className="mb-1 fw-medium">
                          <Badge bg={getStatusVariant(event.status)} className="me-2">
                            {event.status}
                          </Badge>
                          {event.description}
                        </p>
                        {event.notes && <p className="mb-0 text-muted">{event.notes}</p>}
                      </div>
                      <div className="text-muted">{formatDate(event.created_at)}</div>
                    </div>
                  </ListGroup.Item>
                ))}
              </ListGroup>
            ) : (
              <p className="text-center text-muted my-4">Aucun historique disponible pour cette commande.</p>
            )}
          </Card.Body>
        </Card>
      )}
    </Container>
  );
};

export default OrderDetail;
