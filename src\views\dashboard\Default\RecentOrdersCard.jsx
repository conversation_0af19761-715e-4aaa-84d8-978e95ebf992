import PropTypes from 'prop-types';
import React from 'react';

// material-ui
import { useTheme } from '@mui/material/styles';
import Avatar from '@mui/material/Avatar';
import Button from '@mui/material/Button';
import CardActions from '@mui/material/CardActions';
import CardContent from '@mui/material/CardContent';
import Divider from '@mui/material/Divider';
import Grid from '@mui/material/Grid2';
import Typography from '@mui/material/Typography';
import Chip from '@mui/material/Chip';

// project imports
import BajajAreaChartCard from './BajajAreaChartCard';
import MainCard from 'ui-component/cards/MainCard';
import SkeletonPopularCard from 'ui-component/cards/Skeleton/PopularCard';
import { gridSpacing } from 'store/constant';

// assets
import ChevronRightOutlinedIcon from '@mui/icons-material/ChevronRightOutlined';
import ShoppingCartIcon from '@mui/icons-material/ShoppingCart';

export default function RecentOrdersCard({ isLoading, orders, error }) {
  const theme = useTheme();

  const getStatusColor = (status) => {
    switch (status?.toLowerCase()) {
      case 'livré':
      case 'delivered':
        return 'success';
      case 'en cours':
      case 'processing':
        return 'warning';
      case 'annulé':
      case 'cancelled':
        return 'error';
      default:
        return 'default';
    }
  };

  if (error) {
    return (
      <MainCard content={false}>
        <CardContent>
          <Typography variant="h6" color="error">
            Erreur
          </Typography>
          <Typography variant="body2">
            Impossible de charger les commandes récentes
          </Typography>
        </CardContent>
      </MainCard>
    );
  }

  return (
    <>
      {isLoading ? (
        <SkeletonPopularCard />
      ) : (
        <MainCard content={false}>
          <CardContent>
            <Grid container spacing={gridSpacing}>
              <Grid size={12}>
                <Grid container sx={{ alignContent: 'center', justifyContent: 'space-between' }}>
                  <Grid>
                    <Typography variant="h4">Commandes Récentes</Typography>
                  </Grid>
                  <Grid>
                    <Avatar
                      variant="rounded"
                      sx={{
                        ...theme.typography.commonAvatar,
                        ...theme.typography.mediumAvatar,
                        bgcolor: 'primary.light',
                        color: 'primary.dark'
                      }}
                    >
                      <ShoppingCartIcon fontSize="inherit" />
                    </Avatar>
                  </Grid>
                </Grid>
              </Grid>
              
              {orders && orders.length > 0 ? (
                <Grid size={12}>
                  {orders.map((order, index) => (
                    <div key={order.id}>
                      <Grid container direction="column">
                        <Grid>
                          <Grid container sx={{ alignItems: 'center', justifyContent: 'space-between' }}>
                            <Grid>
                              <Typography variant="subtitle1" color="inherit">
                                {order.order_number}
                              </Typography>
                              <Typography variant="subtitle2" sx={{ color: 'grey.500' }}>
                                {order.customer_name}
                              </Typography>
                            </Grid>
                            <Grid>
                              <Grid container sx={{ alignItems: 'center', justifyContent: 'space-between' }}>
                                <Grid>
                                  <Typography variant="subtitle1" color="inherit">
                                    {order.total.toFixed(2)} €
                                  </Typography>
                                </Grid>
                                <Grid sx={{ ml: 1 }}>
                                  <Chip
                                    label={order.status}
                                    color={getStatusColor(order.status)}
                                    size="small"
                                    variant="outlined"
                                  />
                                </Grid>
                              </Grid>
                            </Grid>
                          </Grid>
                        </Grid>
                        <Grid>
                          <Typography variant="subtitle2" sx={{ color: 'grey.500' }}>
                            {new Date(order.created_at).toLocaleDateString('fr-FR')}
                          </Typography>
                        </Grid>
                      </Grid>
                      {index < orders.length - 1 && <Divider sx={{ my: 1.5 }} />}
                    </div>
                  ))}
                </Grid>
              ) : (
                <Grid size={12}>
                  <Typography variant="body2" sx={{ color: 'grey.500', textAlign: 'center', py: 2 }}>
                    Aucune commande récente
                  </Typography>
                </Grid>
              )}
            </Grid>
          </CardContent>
          <CardActions sx={{ p: 1.25, pt: 0, justifyContent: 'center' }}>
            <Button size="small" disableElevation>
              Voir Toutes les Commandes
              <ChevronRightOutlinedIcon />
            </Button>
          </CardActions>
        </MainCard>
      )}
    </>
  );
}

RecentOrdersCard.propTypes = { 
  isLoading: PropTypes.bool,
  orders: PropTypes.array,
  error: PropTypes.string
};

RecentOrdersCard.defaultProps = {
  isLoading: false,
  orders: [],
  error: null
};
