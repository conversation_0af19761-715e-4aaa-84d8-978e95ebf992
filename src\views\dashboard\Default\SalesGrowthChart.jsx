import PropTypes from 'prop-types';
import React, { useState, useEffect } from 'react';

// material-ui
import { useTheme } from '@mui/material/styles';
import Grid from '@mui/material/Grid2';
import MenuItem from '@mui/material/MenuItem';
import TextField from '@mui/material/TextField';
import Typography from '@mui/material/Typography';

// third party
import Chart from 'react-apexcharts';

// project imports
import SkeletonTotalGrowthBarChart from 'ui-component/cards/Skeleton/TotalGrowthBarChart';
import MainCard from 'ui-component/cards/MainCard';
import { gridSpacing } from 'store/constant';

const status = [
  {
    value: 'sales',
    label: 'Ventes'
  },
  {
    value: 'orders',
    label: 'Commandes'
  }
];

export default function SalesGrowthChart({ isLoading, data, error }) {
  const theme = useTheme();
  const [value, setValue] = useState('sales');
  const [chartData, setChartData] = useState({});

  const { primary200, primaryDark, secondaryMain, secondaryLight, primary, darkLight, divider, grey500 } = theme.palette;

  useEffect(() => {
    if (!data || data.length === 0) return;

    const categories = data.map(item => item.month);
    const salesData = data.map(item => item.sales);
    const ordersData = data.map(item => item.orderCount);

    setChartData({
      height: 480,
      type: 'bar',
      options: {
        chart: {
          id: 'bar-chart',
          stacked: false,
          toolbar: {
            show: true
          },
          zoom: {
            enabled: true
          }
        },
        responsive: [
          {
            breakpoint: 480,
            options: {
              legend: {
                position: 'bottom',
                offsetX: -10,
                offsetY: 0
              }
            }
          }
        ],
        plotOptions: {
          bar: {
            horizontal: false,
            columnWidth: '50%'
          }
        },
        xaxis: {
          type: 'category',
          categories: categories,
          labels: {
            style: {
              colors: grey500
            }
          }
        },
        yaxis: {
          labels: {
            style: {
              colors: grey500
            }
          }
        },
        grid: {
          strokeDashArray: 0,
          show: true,
          xaxis: {
            lines: {
              show: false
            }
          },
          yaxis: {
            lines: {
              show: true
            }
          },
          padding: {
            top: 0,
            right: 0,
            bottom: 0,
            left: 0
          }
        },
        fill: {
          type: 'gradient',
          gradient: {
            shade: 'light',
            type: 'vertical',
            shadeIntensity: 0.25,
            gradientToColors: undefined,
            inverseColors: false,
            opacityFrom: 0.85,
            opacityTo: 0.55,
            stops: [50, 0, 100]
          }
        },
        dataLabels: {
          enabled: false
        },
        theme: {
          mode: theme.palette.mode
        }
      },
      series: [
        {
          name: value === 'sales' ? 'Ventes (€)' : 'Nombre de Commandes',
          data: value === 'sales' ? salesData : ordersData,
          color: value === 'sales' ? primary : secondaryMain
        }
      ]
    });
  }, [data, value, primary200, primaryDark, secondaryMain, secondaryLight, primary, darkLight, divider, grey500, theme.palette.mode]);

  if (error) {
    return (
      <MainCard>
        <Typography variant="h6" color="error">
          Erreur
        </Typography>
        <Typography variant="body2">
          Impossible de charger les données de croissance
        </Typography>
      </MainCard>
    );
  }

  return (
    <>
      {isLoading ? (
        <SkeletonTotalGrowthBarChart />
      ) : (
        <MainCard>
          <Grid container spacing={gridSpacing}>
            <Grid size={12}>
              <Grid container sx={{ alignItems: 'center', justifyContent: 'space-between' }}>
                <Grid>
                  <Grid container direction="column" spacing={1}>
                    <Grid>
                      <Typography variant="subtitle2">Évolution des Ventes</Typography>
                    </Grid>
                    <Grid>
                      <Typography variant="h3">
                        {data && data.length > 0 
                          ? value === 'sales' 
                            ? `${data.reduce((sum, item) => sum + item.sales, 0).toFixed(0)} €`
                            : `${data.reduce((sum, item) => sum + item.orderCount, 0)} commandes`
                          : '0 €'
                        }
                      </Typography>
                    </Grid>
                  </Grid>
                </Grid>
                <Grid>
                  <TextField 
                    id="standard-select-currency" 
                    select 
                    value={value} 
                    onChange={(e) => setValue(e.target.value)}
                  >
                    {status.map((option) => (
                      <MenuItem key={option.value} value={option.value}>
                        {option.label}
                      </MenuItem>
                    ))}
                  </TextField>
                </Grid>
              </Grid>
            </Grid>
            <Grid size={12}>
              {chartData.series && chartData.series.length > 0 ? (
                <Chart {...chartData} />
              ) : (
                <Typography variant="body2" sx={{ color: 'grey.500', textAlign: 'center', py: 4 }}>
                  Aucune donnée disponible
                </Typography>
              )}
            </Grid>
          </Grid>
        </MainCard>
      )}
    </>
  );
}

SalesGrowthChart.propTypes = { 
  isLoading: PropTypes.bool,
  data: PropTypes.array,
  error: PropTypes.string
};

SalesGrowthChart.defaultProps = {
  isLoading: false,
  data: [],
  error: null
};
